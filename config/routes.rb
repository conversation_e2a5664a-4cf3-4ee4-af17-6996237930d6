Rails.application.routes.draw do
  apipie

  namespace :api do
    resources :employees do
      resources :user_roles, controller: 'employees/user_roles', only: [ :index, :show, :create, :destroy ]
      resources :attachments,
                controller: :employee_attachments,
                path: :attachment,
                only: [ :index, :show, :create, :update, :destroy ]
      resources :leaves, only: [ :index, :show, :create, :update, :destroy ] do
        member do
          post :withdraw
        end
      end
      resources :attendance_events, only: [ :index, :show, :create, :update, :destroy ]
      # resources :attendance_summaries, only: [ :index ] do
      #   collection do
      #     post :recalculate
      #   end
      # end

      resources :salary_packages, only: [ :index, :show ], controller: 'finance/salary_packages'
      resources :salary_calculations, only: [ :index, :show ], controller: 'finance/salary_calculations' do
        member do
          get :download_slip
        end
      end
    end

    # Namespaced attendance routes
    namespace :attendance do
      resources :events do
        collection do
          get :undetermined
          post :resolve
          post :suggest
          post :auto_resolve
          post :batch_create
          post :trigger_resolution
          post :record
        end
      end

      resources :devices do
        member do
          post :test_connection
          post :sync
          get :device_info
        end

        collection do
          post :multi_sync
          get :health_report
        end

        resources :users, controller: 'device/users' do
          collection do
            post :sync_users
          end
        end

        resources :commands, controller: 'device/commands', only: [ :index, :create ] do
          collection do
            get :history
          end
        end

        resources :mappings, controller: 'device/mappings' do
          collection do
            get :unmapped_users
          end
        end

        # Individual employee sync
        post 'sync_employee/:employee_id', to: 'device/users#sync_employee', as: :device_sync_employee
      end

      resources :sync_logs, only: [ :index, :show ] do
        collection do
          delete :cleanup
          get :statistics
          get :recent_failures
        end
      end

      resources :periods, only: [ :index ] do
        collection do
          post :recalculate
          get :summary
          get :daily_records
          get :month_statistics
        end
      end

      resources :summaries, only: [ :index, :show ] do
        collection do
          post :recalculate
          get :statistics
        end
      end
    end

    resources :settings

    resources :statistics, only: [ :index ]

    resources :leaves, only: [ :index, :show, :create, :update, :destroy ] do
      member do
        post :withdraw
      end
      resources :documents,
                controller: :leave_documents,
                only: [ :index, :show, :create, :update, :destroy ]
    end

    resources :approval_requests, only: [ :index, :show, :create, :update, :destroy ] do
      collection do
        get :pending_approvals
      end
      member do
        post :approve
        post :reject
        post :cancel
      end
    end

    resources :employees do
      resources :device_mappings, controller: 'employees/device_mappings', only: [ :index, :create, :destroy ]
    end
  end

  # Finance routes
  namespace :api do
    namespace :finance do
      resources :salary_packages, only: [ :index, :create ]

      resources :salary_calculations do
        collection do
          post :calculate_period
          post :calculate_custom
        end
        member do
          get :download_slip
        end
      end

      resources :tax_configs
      resources :social_security_configs
    end
  end

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Sidekiq Web UI
  require 'sidekiq/web'

  # Make sure sidekiq-scheduler web UI is loaded
  begin
    require 'sidekiq-scheduler/web'
  rescue LoadError
    # Handle the case where sidekiq-scheduler is not available
    puts "WARNING: sidekiq-scheduler/web could not be loaded"
  end

  # Commenting out due to compatibility issues with Sidekiq 7.3.9
  # require 'sidekiq-statistic'

  # Secure the Sidekiq Web UI with basic auth in production
  if Rails.env.production?
    Sidekiq::Web.use Rack::Auth::Basic do |username, password|
      # Replace with a real check in production
      ActiveSupport::SecurityUtils.secure_compare(username, ENV["SIDEKIQ_USERNAME"]) &
        ActiveSupport::SecurityUtils.secure_compare(password, ENV["SIDEKIQ_PASSWORD"])
    end
  end

  mount Sidekiq::Web => '/sidekiq'

  # Defines the root path route ("/")
  root "application#index"
end
