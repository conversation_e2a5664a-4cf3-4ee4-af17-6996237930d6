# frozen_string_literal: true

class EmployeeDeviceMappingSerializer < Athar::Commons::Api::BaseSerializer
  # Attributes
  attributes :id, :device_user_id, :notes, :created_at, :updated_at

  # Relationships
  belongs_to :employee, serializer: EmployeeSerializer
  belongs_to :attendance_device, serializer: Attendance::DeviceSerializer

  # Custom attributes
  attribute :device_name do |mapping|
    mapping.attendance_device&.name
  end

  attribute :employee_name do |mapping|
    mapping.employee&.name
  end

  attribute :device_adapter_type do |mapping|
    mapping.attendance_device&.adapter_type
  end

  attribute :device_status do |mapping|
    mapping.attendance_device&.status
  end

  # Meta information
  meta do |mapping, params|
    device_supports_user_management = begin
      mapping.attendance_device&.create_adapter&.supports_user_management?
    rescue
      false
    end

    {
      mapping_status: mapping.attendance_device&.status,
      device_supports_user_management: device_supports_user_management,
      last_sync_at: mapping.attendance_device&.last_sync_log&.completed_at
    }
  end
end
