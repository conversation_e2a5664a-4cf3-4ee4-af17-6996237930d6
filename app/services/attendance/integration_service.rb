module Attendance
  class IntegrationService
    attr_reader :adapter, :options

    # Initialize with a specific adapter type
    def initialize(adapter_type = :zkteco, options = {})
      @options = options
      @adapter = create_adapter(adapter_type)
    end

    # Fetch attendance data from the source
    def fetch_data(start_date = Date.yesterday, end_date = Date.today)
      adapter.fetch_data(start_date, end_date)
    end

    # Import attendance data into the system
    def import_data(data)
      return { success: 0, failure: 0 } if data.blank?

      results = { success: 0, failure: 0 }
      affected_summaries = {}

      # Process each attendance record
      data.each do |record|
        # Find or create employee by employee_code from device
        employee = find_employee(record[:employee_code])
        next unless employee

        # Create attendance event
        event = AttendanceEvent.new(
          employee: employee,
          timestamp: record[:timestamp],
          event_type: :undetermined, # Default to undetermined, will be resolved later
          activity_type: :break, # Default activity type
          location: record[:location] || adapter.device_name,
          notes: "Imported from #{adapter.device_name} on #{Time.current}"
        )

        if event.save
          results[:success] += 1

          # Track which summaries need to be updated
          date = event.timestamp.to_date
          affected_summaries[event.employee_id] ||= Set.new
          affected_summaries[event.employee_id] << date
        else
          results[:failure] += 1
          Rails.logger.error("Failed to import attendance record: #{record.inspect}, Errors: #{event.errors.full_messages}")
        end
      end

      # Update all affected summaries
      affected_summaries.each do |employee_id, dates|
        employee = Employee.find(employee_id)
        dates.each do |date|
          ::Attendance::Summary.recalculate_for(employee, date)
        end
      end

      # Queue resolution job for the imported dates
      unique_dates = affected_summaries.values.flatten.uniq
      unique_dates.each do |date|
        Attendance::ResolutionWorker.perform_async(date.to_s)
      end

      results
    end

    # Sync attendance data (fetch and import in one step)
    def sync(start_date = Date.yesterday, end_date = Date.today)
      data = fetch_data(start_date, end_date)
      import_data(data)
    end

    private

    def create_adapter(adapter_type)
      case adapter_type.to_sym
      when :zkteco
        Attendance::Adapters::ZktecoAdapter.new(options)
      when :file_import
        Attendance::Adapters::FileImportAdapter.new(options)
      else
        raise ArgumentError, "Unknown adapter type: #{adapter_type}"
      end
    end

    def find_employee(employee_code)
      # Find employee by their device code (could be stored in a separate field or metadata)
      employee = Employee.find_by(device_code: employee_code)

      unless employee
        Rails.logger.warn("Employee not found for device code: #{employee_code}")
      end

      employee
    end
  end
end
