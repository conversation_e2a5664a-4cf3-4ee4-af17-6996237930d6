# frozen_string_literal: true

module Statistics
  module Calculators
    class AbsencesCalculator
      include BaseCalculator
      include Statistics::Helpers::DateHelpers
      include Statistics::Helpers::EmployeeHelpers
      include Statistics::Helpers::CardHelpers

      def validate_context(context)
        unless context[:employee_id].present?
          raise ArgumentError, "Missing required context: employee_id must be provided for absences calculator"
        end

        context[:employee] = find_employee(context[:employee_id])
        context[:start_date] = parse_date_with_formats(context[:start_date]) || Date.today.beginning_of_month
        context[:end_date] = parse_date_with_formats(context[:end_date]) || Date.today

        if context[:end_date] < context[:start_date]
          raise ArgumentError, "End date cannot be before start date"
        end

        context[:comparison_period] = (context[:comparison_period] || 'previous').to_sym
        context[:comparison_text] = generate_comparison_text(context[:comparison_period])
      rescue ArgumentError => e
        raise ArgumentError, e.message
      rescue => e
        raise ArgumentError, "Invalid date format: #{e.message}"
      end

      def perform_calculation(context)
        employee = context[:employee]
        start_date = context[:start_date]
        end_date = context[:end_date]
        comparison_period = context[:comparison_period]

        current_absences = count_absences(employee, start_date, end_date)

        prev_start_date, prev_end_date = calculate_previous_period(start_date, end_date, comparison_period)
        previous_absences = count_absences(employee, prev_start_date, prev_end_date)

        comparison_text = context[:comparison_text] || generate_comparison_text(comparison_period)

        create_metric_card(
          card_id,
          'Absences',
          current_absences,
          'days',
          current_absences,
          previous_absences,
          comparison_text
        )
      end

      private

      def count_absences(employee, start_date, end_date)
        absences = 0
        (start_date..end_date).each do |workday|
          next if [ 0, 6 ].include?(workday.wday) # Skip weekends
          day_periods = Attendance::Period.where(employee: employee, date: workday)
          absences += 1 if day_periods.empty?
        end
        absences
      end
    end
  end
end
