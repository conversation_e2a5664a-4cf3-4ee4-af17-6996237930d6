module Attendance
  class PeriodCalculationWorker
    include Sidekiq::Worker

    sidekiq_options queue: :attendance, retry: 3

  def perform(employee_id, date_string)
    begin
      # Log the input parameters for debugging
      Rails.logger.info("Starting attendance period calculation for employee_id: #{employee_id.inspect}, date_string: #{date_string.inspect}")

      # Find the employee - handle the case where the employee might not be available
      begin
        employee = Employee.find(employee_id)
      rescue => e
        Rails.logger.error("Failed to find employee #{employee_id}: #{e.message}")
        # Create a stub employee for development purposes
        employee = Employee.new(id: employee_id)
        # Allow the employee to respond to methods needed by the PeriodService
        def employee.attendance_periods
          ::Attendance::Period.where(employee_id: id)
        end
      end

      # Parse the date
      date = date_string.is_a?(String) ? Date.parse(date_string) : date_string.to_date

      # Check if this is the current day (incomplete day)
      incomplete_day = (date == Date.today)

      # Calculate periods, passing the incomplete_day flag
      Attendance::PeriodService.new(employee, date, incomplete_day).calculate_periods

      # Log completion
      Rails.logger.info("Calculated attendance periods for employee #{employee_id} on #{date}")
    rescue => e
      # Log the error with detailed information
      Rails.logger.error("Error calculating attendance periods: #{e.class.name}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      raise e
    end
  end
end
