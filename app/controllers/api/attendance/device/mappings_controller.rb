# frozen_string_literal: true

module Api
  module Attendance
    module Device
      class MappingsController < Athar::Commons::Api::BaseController
        before_action :authenticate_session!
        before_action :set_device
        before_action :set_mapping, only: [ :show, :update, :destroy ]
        before_action :authorize_read, only: [ :index, :show, :unmapped_users ]
        before_action :authorize_create, only: [ :create ]
        before_action :authorize_update, only: [ :update ]
        before_action :authorize_destroy, only: [ :destroy ]

        api! "Lists employee-device mappings for a specific device"
        header "Authorization", "Scoped session token as Bearer token", required: true
        param :device_id, Integer, required: true, desc: "Device ID"
        param_group :pagination_params
        param_group :filter_params
        param_group :sort_params
        param_group :include_params
        param_group :fields_params
        description self.jsonapi_with_docs(<<-HTML
          <b>Endpoint Details</b>

          Returns a list of employee-device mappings for the specified device.
          Shows which employees are mapped to which device user IDs.
          Supports filtering, sorting, and pagination.
          Requires permission: <code>:read, :attendance_device</code>.
        HTML
        )
        returns code: 200, desc: "List of device mappings"
        error code: 404, desc: "Device not found"

        def index
          @collection = @device.employee_device_mappings.with_full_data

          apply_filters(@collection) do |filtered_and_sorted|
            records, meta = paginate(filtered_and_sorted)

            # Add device context to meta
            meta[:device] = {
              id: @device.id,
              name: @device.name,
              adapter_type: @device.adapter_type,
              total_mappings: @collection.count
            }

            serialize_response(records, serializer: EmployeeDeviceMappingSerializer, meta: meta)
          end
        end

        api! "Shows a specific employee-device mapping"
        header "Authorization", "Scoped session token as Bearer token", required: true
        param :device_id, Integer, required: true, desc: "Device ID"
        param :id, Integer, required: true, desc: "Mapping ID"
        param_group :include_params
        param_group :fields_params
        description self.jsonapi_with_docs(<<-HTML
          <b>Endpoint Details</b>

          Shows details for a specific employee-device mapping.
          Requires permission: <code>:read, :attendance_device</code>.
        HTML
        )
        returns code: 200, desc: "Mapping details"
        error code: 404, desc: "Mapping not found"

        def show
          serialize_response(@mapping, serializer: EmployeeDeviceMappingSerializer)
        end

        api! "Creates a new employee-device mapping"
        header "Authorization", "Scoped session token as Bearer token", required: true
        param :device_id, Integer, required: true, desc: "Device ID"
        param :employee_device_mapping, Hash, required: true, desc: "Mapping attributes" do
          param :employee_id, Integer, required: true, desc: "Employee ID"
          param :device_user_id, String, required: true, desc: "Device user ID"
          param :notes, String, desc: "Optional notes"
        end
        description self.jsonapi_with_docs(<<-HTML
          <b>Endpoint Details</b>

          Creates a new employee-device mapping for the specified device.
          Requires permission: <code>:create, :attendance_device</code>.
        HTML
        )
        returns code: 201, desc: "Mapping created successfully"
        error code: 422, desc: "Validation errors"

        def create
          @mapping = @device.employee_device_mappings.build(mapping_params)

          if @mapping.save
            serialize_response(@mapping, status: :created, serializer: EmployeeDeviceMappingSerializer)
          else
            serialize_errors(@mapping.errors)
          end
        end

        api! "Updates an employee-device mapping"
        header "Authorization", "Scoped session token as Bearer token", required: true
        param :device_id, Integer, required: true, desc: "Device ID"
        param :id, Integer, required: true, desc: "Mapping ID"
        param :employee_device_mapping, Hash, required: true, desc: "Mapping attributes" do
          param :device_user_id, String, desc: "Device user ID"
          param :notes, String, desc: "Optional notes"
        end
        description self.jsonapi_with_docs(<<-HTML
          <b>Endpoint Details</b>

          Updates an existing employee-device mapping.
          Note: Employee cannot be changed - create a new mapping instead.
          Requires permission: <code>:update, :attendance_device</code>.
        HTML
        )
        returns code: 200, desc: "Mapping updated successfully"
        error code: 422, desc: "Validation errors"

        def update
          if @mapping.update(update_mapping_params)
            serialize_response(@mapping, serializer: EmployeeDeviceMappingSerializer)
          else
            serialize_errors(@mapping.errors)
          end
        end

        api! "Deletes an employee-device mapping"
        header "Authorization", "Scoped session token as Bearer token", required: true
        param :device_id, Integer, required: true, desc: "Device ID"
        param :id, Integer, required: true, desc: "Mapping ID"
        description self.jsonapi_with_docs(<<-HTML
          <b>Endpoint Details</b>

          Deletes an employee-device mapping.
          This will remove the association between the employee and device user.
          Requires permission: <code>:destroy, :attendance_device</code>.
        HTML
        )
        returns code: 204, desc: "Mapping deleted successfully"

        def destroy
          @mapping.destroy!
          head :no_content
        end

        api! "Lists device users without employee mappings"
        header "Authorization", "Scoped session token as Bearer token", required: true
        param :device_id, Integer, required: true, desc: "Device ID"
        description self.jsonapi_with_docs(<<-HTML
          <b>Endpoint Details</b>

          Returns device users that don't have employee mappings.
          Useful for discovering unmapped users that need manual mapping.
          Requires permission: <code>:read, :attendance_device</code>.
        HTML
        )
        returns code: 200, desc: "List of unmapped device users"
        error code: 422, desc: "Device does not support user management"

        def unmapped_users
          unless @device.create_adapter.supports_user_management?
            serialize_errors({
                               detail: "Device does not support user management",
                               device_type: @device.adapter_type
                             }, :unprocessable_entity)
            return
          end

          unmapped_users = EmployeeDeviceMapping.unmapped_device_users(@device)

          render json: {
            device_id: @device.id,
            device_name: @device.name,
            unmapped_users: unmapped_users,
            count: unmapped_users.size,
            retrieved_at: Time.current.iso8601
          }
        rescue => e
          Rails.logger.error("Failed to get unmapped users for device #{@device.name}: #{e.message}")
          serialize_errors({
                             detail: "Failed to retrieve unmapped users",
                             error: e.message
                           }, :service_unavailable)
        end

        private

        def set_device
          @device = ::Attendance::Device.find(params[:device_id])
        rescue ActiveRecord::RecordNotFound
          serialize_errors({ detail: "Device not found" }, :not_found)
        end

        def set_mapping
          @mapping = @device.employee_device_mappings.find(params[:id])
        rescue ActiveRecord::RecordNotFound
          serialize_errors({ detail: "Mapping not found" }, :not_found)
        end

        def mapping_params
          params.require(:employee_device_mapping).permit(:employee_id, :device_user_id, :notes)
        end

        def update_mapping_params
          params.require(:employee_device_mapping).permit(:device_user_id, :notes)
        end

        # Authorization methods
        def authorize_read
          authorize!(:read, :attendance_device)
        end

        def authorize_create
          authorize!(:create, :attendance_device)
        end

        def authorize_update
          authorize!(:update, :attendance_device)
        end

        def authorize_destroy
          authorize!(:destroy, :attendance_device)
        end
      end
    end
  end
end
